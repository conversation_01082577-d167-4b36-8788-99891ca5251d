package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.FmsUserRoleDO;
import org.apache.ibatis.annotations.*;

import javax.validation.constraints.NotNull;

@Mapper
public interface UserRoleMapper extends BaseMapper<FmsUserRoleDO> {

    @Select("select * from FmsUserRole where UserId = #{userId} and RoleId = #{roleId}")
    FmsUserRoleDO findByUserIdAndRoleId(@Param("userId") int userId, @Param("roleId") int roleId);

    @Select("select top 1 * from FmsUserRole where UserId = #{userId}")
    FmsUserRoleDO findByUserId(@Param("userId") int userId);

    @Insert("insert into FmsUserRole (UserId, RoleId) values (#{userId}, #{roleId})")
    void bindUserRole(@Param("userId") int userId, @Param("roleId") int roleId);

    @Update("update FmsUserRole set RoleId = #{roleId} where UserId = #{userId}")
    void modifyUserRole(@Param("userId") int userId, @Param("roleId") int roleId);

    @Select("select count(UserId) from FmsUserRole where RoleId = #{roleId}")
    long countUserWithRoleId(@Param("roleId") Integer roleId);
}
