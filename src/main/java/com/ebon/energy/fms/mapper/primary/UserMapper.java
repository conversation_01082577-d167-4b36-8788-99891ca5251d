package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.FmsUserDO;
import com.ebon.energy.fms.domain.vo.UserListVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface UserMapper extends BaseMapper<FmsUserDO> {

    @Select("select * from FmsUser where email=#{email}")
    FmsUserDO selectByEmail(@Param("email") String email);

//    @Insert("insert into FmsUser (name, email, status) values (#{name}, #{email}, #{status}) ")
//    void addUser(@Param("name") String name, @Param("email") String email, @Param("status") Boolean status);

    @Update("update FmsUser set name = #{name}, status = #{status} where id = #{id} ")
    void modifyUser(@Param("id") Integer id, @Param("name") String name, @Param("status") Boolean status);

    @Select("<script>" +
            "select u.Id, u.Name, u.Email, u.Status, u.LastLoginTime, r.<PERSON>, r.id as roleId " +
            "from FmsUser u " +
            "left join FmsUserRole ur on u.id = ur.UserId " +
            "left join FmsRole r on ur.RoleId = r.id " +
            "<where>" +
            "   <if test='userName != null and userName != \"\"'>" +
            "       AND u.Name LIKE '%' + #{userName} + '%' " +
            "   </if>" +
            "   <if test='roleId != null'>" +
            "       AND r.id = #{roleId}" +
            "   </if>" +
            "</where>" +
            "order by u.Id ASC " +
            "offset #{offset} rows " +
            "fetch next #{pageSize} rows only " +
            "</script>")
    List<UserListVO> selectUserList(@Param("userName") String userName,
                                    @Param("roleId") Integer roleId,
                                    @Param("offset") Integer offset,
                                    @Param("pageSize")Integer pageSize);

    @Select("<script>" +
            "select count(u.Id) " +
            "from FmsUser u " +
            "left join FmsUserRole ur on u.id = ur.UserId " +
            "left join FmsRole r on ur.RoleId = r.id " +
            "<where>" +
            "   <if test='userName != null and userName != \"\"'>" +
            "       AND u.Name LIKE '%' + #{userName} + '%' " +
            "   </if>" +
            "   <if test='roleId != null'>" +
            "       AND r.id = #{roleId}" +
            "   </if>" +
            "</where>" +
            "</script>")
    Long countUserList(@Param("userName") String userName, @Param("roleId") Integer roleId);
}
