package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InverterAttentionDO;
import com.ebon.energy.fms.domain.entity.MetricsTelemetryDO;
import com.ebon.energy.fms.domain.entity.RedbackProductsDO;
import com.ebon.energy.fms.domain.vo.product.control.UpdateProductControlInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface MetricsTelemetryMapper extends BaseMapper<MetricsTelemetryDO> {

    @Select("SELECT p.RedbackProductSn,p.LastSystemStatusReceived,mt.IncidentsForHomeUser,mt.IncidentsForInstaller,mt.IncidentsForRb " +
            "FROM dbo.RedbackProducts p WITH(NOLOCK) " +
            "LEFT JOIN dbo.RedbackProductInstallations i WITH(NOLOCK) ON i.RedbackProductSn = p.RedbackProductSn AND i.InstallationEndDate IS NULL " +
            "LEFT JOIN dbo.MetricsTelemetry mt WITH(NOLOCK) ON mt.SerialNumber = p.RedbackProductSn " +
            "LEFT JOIN dbo.Addresses a ON a.Id = i.AddressId " +
            "LEFT JOIN dbo.RedbackProductInstallationDetails details ON details.RedbackProductInstallationId = i.Id " +
            "AND details.Name = 'IsSupposedToHaveInternetConnection' " +
            "WHERE " +
            "    p.IsInWarranty <> 1" +
            "    AND (details.Value IS NULL OR details.Value <> 'True')" +
            "    AND (a.AddressLineOne IS NOT NULL AND a.AddressLineOne <> '' AND TRIM(a.AddressLineOne) <> ',')" +
            "    AND (p.LastSystemStatusReceived IS NOT NULL)" +
            "    AND (mt.MinutesSinceLastTelemetry IS NOT NULL AND mt.MinutesSinceLastTelemetry <= 10)" +
            "    AND ( " +
            "        (mt.IncidentsForHomeUser > 0) OR " +
            "        (mt.IncidentsForInstaller > 0) OR " +
            "        (mt.IncidentsForRb > 0) " +
            "    )")
    List<InverterAttentionDO> selectAttention();

    @Select("<script>" +
            "select p.RedbackProductSn as serialNumber,  " +
            "    isnull(mt.IncidentsForHomeUser,0) IncidentsForHomeUser, " +
            "    isnull(mt.IncidentsForInstaller,0) IncidentsForInstaller, " +
            "    isnull(mt.IncidentsForRb,0) IncidentsForRb " +
            "from redbackproducts p " +
            "left join MetricsTelemetry mt WITH(NOLOCK) on p.RedbackProductSn = mt.SerialNumber " +
            "where p.RedbackProductSn in " +
            "<foreach item='item' collection='serialNumbers' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<MetricsTelemetryDO> selectInSns(@Param("serialNumbers") List<String> serialNumbers);

}
