package com.ebon.energy.fms.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserListVO {

    /**
     * id
     */
    private Integer id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 最后登录时间
     */
    private Timestamp lastLoginTime;
    /**
     * role id
     */
    private Integer roleId;
}
