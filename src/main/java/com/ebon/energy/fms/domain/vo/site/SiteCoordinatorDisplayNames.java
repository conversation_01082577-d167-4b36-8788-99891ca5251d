package com.ebon.energy.fms.domain.vo.site;

import com.ebon.energy.fms.common.enums.SiteRole;

import java.util.HashMap;
import java.util.Map;

public class SiteCoordinatorDisplayNames {
    private final Map<String, String> names = new HashMap<>();

    public SiteCoordinatorDisplayNames() {
        names.put(SiteRole.Coordinator.name(), "Master");
    }

    public String getDisplayName(String value) {
        return names.getOrDefault(value, value);
    }
}
