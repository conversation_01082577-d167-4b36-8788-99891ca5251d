package com.ebon.energy.fms.domain.entity;

import com.ebon.energy.fms.domain.vo.product.control.RossVersion;

import java.time.LocalDateTime;
import java.util.Optional;

public class SiteCoordinatorSiteDataDO {
    private String serialNumber;
    private Boolean isConnected;
    private String version;
    private String model;
    private String manufactureDate;
    private Integer manufactureSerialNumber;
    private String siteRole;
    private boolean isGridTie;
    private String siteId;

    // 构造方法
    public SiteCoordinatorSiteDataDO() {
        // 默认构造函数
    }

    // Getters and Setters
    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Boolean getIsConnected() {
        return isConnected;
    }

    public void setIsConnected(Boolean connected) {
        isConnected = connected;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getManufactureDate() {
        return manufactureDate;
    }

    public void setManufactureDate(String manufactureDate) {
        this.manufactureDate = manufactureDate;
    }

    public Integer getManufactureSerialNumber() {
        return  manufactureSerialNumber;
    }

    public void setManufactureSerialNumber(Integer manufactureSerialNumber) {
        this.manufactureSerialNumber = manufactureSerialNumber;
    }

    public String getSiteRole() {
        return siteRole;
    }

    public void setSiteRole(String siteRole) {
        this.siteRole = siteRole;
    }

    public boolean isGridTie() {
        return isGridTie;
    }

    public void setGridTie(boolean gridTie) {
        isGridTie = gridTie;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }
}