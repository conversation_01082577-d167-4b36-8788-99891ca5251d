package com.ebon.energy.fms.domain.vo.site;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.ICommonSettingsReader;
import com.ebon.energy.fms.util.SafeAccess;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

@Data
public class GenericInverterConfiguration {
    private final BigDecimal minSoC0to1;
    private final BigDecimal minSoCOffGrid0to1;
    private final Ampere maxDischargeCurrent;
    private final Ampere maxChargeCurrent;
    private final Volt maxChargeVoltage;
    private final Watt functionalSiteExportLimit;
    private final VoltAmps functionalHardGenerationLimit;
    private final VoltAmps functionalSoftGenerationLimit;
    private final Watt functionalHardExportLimit;
    private final Watt functionalSoftExportLimit;
    private final Boolean hasBatteries;

    public GenericInverterConfiguration(
            BigDecimal minSoC0to1,
            BigDecimal minSoCOffGrid0to1,
            Ampere maxDischargeCurrent,
            Ampere maxChargeCurrent,
            Volt maxChargeVoltage,
            Watt functionalSiteExportLimit,
            Boolean hasBatteries) {
        this(
                minSoC0to1,
                minSoCOffGrid0to1,
                maxDischargeCurrent,
                maxChargeCurrent,
                maxChargeVoltage,
                functionalSiteExportLimit,
                hasBatteries,
                null,
                null,
                null,
                null
        );
    }

    public GenericInverterConfiguration(
            BigDecimal minSoC0to1,
            BigDecimal minSoCOffGrid0to1,
            Ampere maxDischargeCurrent,
            Ampere maxChargeCurrent,
            Volt maxChargeVoltage,
            Watt functionalSiteExportLimit,
            Boolean hasBatteries,
            VoltAmps functionalHardGenerationLimit,
            VoltAmps functionalSoftGenerationLimit,
            Watt functionalHardExportLimit,
            Watt functionalSoftExportLimit) {
        this.minSoC0to1 = minSoC0to1;
        this.minSoCOffGrid0to1 = minSoCOffGrid0to1;
        this.maxDischargeCurrent = maxDischargeCurrent;
        this.maxChargeCurrent = maxChargeCurrent;
        this.maxChargeVoltage = maxChargeVoltage;
        this.functionalSiteExportLimit = functionalSiteExportLimit;
        this.hasBatteries = hasBatteries;
        this.functionalHardGenerationLimit = functionalHardGenerationLimit;
        this.functionalSoftGenerationLimit = functionalSoftGenerationLimit;
        this.functionalHardExportLimit = functionalHardExportLimit;
        this.functionalSoftExportLimit = functionalSoftExportLimit;
    }

    public static GenericInverterConfiguration fromSettings(ICommonSettingsReader reader) {
        // 从Reported和Desired中获取电池设置
        BatterySettingsDto batteryReported = reader.getBatterySettings(UniversalSettingSource.REPORTED);
        BatterySettingsDto batteryDesired = reader.getBatterySettings(UniversalSettingSource.DESIRED);
        SiteLimits siteLimits = reader.getSiteLimits();

        // 提取制造商信息（从Desired获取，因为规则是Desired中的None表示没有电池）
        String manufacturer = Optional.ofNullable(batteryDesired)
                .map(BatterySettingsDto::getManufacturer)
                .orElse(ManufacturerEnum.None.toString());

        Integer batteryMinSoC = Optional.ofNullable(batteryReported)
                .map(BatterySettingsDto::getMinSoc)
                .map(MinMaxValueDto::getValue)
                .orElseGet(() ->
                        // 若 batteryReported 为空或其 MinSoc 为空，则使用 batteryDesired.MinSoc.Value
                        Optional.ofNullable(batteryDesired)
                                .map(BatterySettingsDto::getMinSoc)
                                .map(MinMaxValueDto::getValue)
                                .orElse(null)
                );
        // 计算SoC值（除以100转换为小数）
        BigDecimal minSoC = batteryMinSoC != null ? new BigDecimal(String.valueOf(batteryMinSoC)).divide(new BigDecimal("100")) : null;

        Integer batteryMinOffgridSoc = Optional.ofNullable(batteryReported)
                .map(BatterySettingsDto::getMinOffgridSoc)
                .map(MinMaxValueDto::getValue)
                .orElseGet(() ->
                        // 若 batteryReported 为空或其 MinSoc 为空，则使用 batteryDesired.MinSoc.Value
                        Optional.ofNullable(batteryDesired)
                                .map(BatterySettingsDto::getMinOffgridSoc)
                                .map(MinMaxValueDto::getValue)
                                .orElse(null)
                );
        BigDecimal minSoCOffGrid = batteryMinOffgridSoc != null ? new BigDecimal(String.valueOf(batteryMinOffgridSoc)).divide(new BigDecimal("100")) : null;

        // 获取电流和电压值
        Integer batteryMaxDischargeCurrent = Optional.ofNullable(batteryReported)
                .map(BatterySettingsDto::getMaxDischargeCurrent)
                .map(MinMaxValueDto::getValue)
                .orElseGet(() ->
                        // 若 batteryReported 为空或其 MinSoc 为空，则使用 batteryDesired.MinSoc.Value
                        Optional.ofNullable(batteryDesired)
                                .map(BatterySettingsDto::getMaxDischargeCurrent)
                                .map(MinMaxValueDto::getValue)
                                .orElse(null)
                );
        Ampere maxDischargeCurrent = batteryMaxDischargeCurrent != null ? new Ampere(batteryMaxDischargeCurrent) : null;

        Integer batteryMaxChargeCurrent = Optional.ofNullable(batteryReported)
                .map(BatterySettingsDto::getMaxChargeCurrent)
                .map(MinMaxValueDto::getValue)
                .orElseGet(() ->
                        // 若 batteryReported 为空或其 MinSoc 为空，则使用 batteryDesired.MinSoc.Value
                        Optional.ofNullable(batteryDesired)
                                .map(BatterySettingsDto::getMaxChargeCurrent)
                                .map(MinMaxValueDto::getValue)
                                .orElse(null)
                );
        Ampere maxChargeCurrent = batteryMaxChargeCurrent != null ? new Ampere(batteryMaxChargeCurrent) : null;

        Volt maxChargeVoltage = new Volt(SafeAccess.getOrElse(batteryReported, BatterySettingsDto::getMaxChargeVoltageV,
                SafeAccess.getValue(batteryDesired, BatterySettingsDto::getMaxChargeVoltageV)));

        // 站点限制
        Watt functionalSiteExportLimit = Optional.ofNullable(siteLimits)
                .map(SiteLimits::getFunctionalSiteExportLimitW)
                .orElse(null);
        VoltAmps functionalHardGenerationLimit = Optional.ofNullable(siteLimits)
                .map(SiteLimits::getHardSiteGenerationLimitVA)
                .orElse(null);
        VoltAmps functionalSoftGenerationLimit = Optional.ofNullable(siteLimits)
                .map(SiteLimits::getSoftSiteGenerationLimitVA)
                .orElse(null);
        Watt functionalHardExportLimit = Optional.ofNullable(siteLimits)
                .map(SiteLimits::getHardSiteExportLimitW)
                .orElse(null);
        Watt functionalSoftExportLimit = Optional.ofNullable(siteLimits)
                .map(SiteLimits::getSoftSiteExportLimitW)
                .orElse(null);

        // 判断是否有电池
        boolean hasBatteries = !ManufacturerEnum.None.name().equals(manufacturer) && !manufacturer.isEmpty();

        return new GenericInverterConfiguration(
                minSoC,
                minSoCOffGrid,
                maxDischargeCurrent,
                maxChargeCurrent,
                maxChargeVoltage,
                functionalSiteExportLimit,
                hasBatteries,
                functionalHardGenerationLimit,
                functionalSoftGenerationLimit,
                functionalHardExportLimit,
                functionalSoftExportLimit
        );
    }

}