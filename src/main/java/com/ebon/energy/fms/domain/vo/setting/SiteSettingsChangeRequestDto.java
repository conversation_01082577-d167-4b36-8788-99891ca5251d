package com.ebon.energy.fms.domain.vo.setting;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

public class SiteSettingsChangeRequestDto {

    private boolean modbusServerEnable;

    private String inverterRole;

    private String executionInterval;

    private Map<String, PeerInfoDto> peers = new HashMap<>();

    // 构造方法
    public SiteSettingsChangeRequestDto() {
    }

    // Getter 和 Setter
    public boolean isModbusServerEnable() {
        return modbusServerEnable;
    }

    public void setModbusServerEnable(boolean modbusServerEnable) {
        this.modbusServerEnable = modbusServerEnable;
    }

    public String getInverterRole() {
        return inverterRole;
    }

    public void setInverterRole(String inverterRole) {
        this.inverterRole = inverterRole;
    }

    public String getExecutionInterval() {
        return executionInterval;
    }

    public void setExecutionInterval(String executionInterval) {
        this.executionInterval = executionInterval;
    }

    public Map<String, PeerInfoDto> getPeers() {
        return peers;
    }

    public void setPeers(Map<String, PeerInfoDto> peers) {
        this.peers = peers;
    }

    public void addPeer(String key, PeerInfoDto value) {
        this.peers.put(key, value);
    }
}
