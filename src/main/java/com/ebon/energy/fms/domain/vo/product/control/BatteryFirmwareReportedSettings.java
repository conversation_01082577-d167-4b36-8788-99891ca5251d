package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@EqualsAndHashCode
public class BatteryFirmwareReportedSettings {

    @JsonProperty("PylonHVCmu")
    private String pylonHVCmu;

    @JsonProperty("PylonHVBmu")
    private String pylonHVBmu;

    /**
     * 获取配置差异
     */
    public static List<ConfigurationDifference> getDifferences(
            BatteryFirmwareReportedSettings reported,
            BatteryFirmwareDesiredSettings desired) {
        List<ConfigurationDifference> differences = new ArrayList<>();

        ConfigurationDifference.add(differences, "pylonHVBmu",
                desired != null ? desired.getPylonHVBmu() : null,
                reported != null ? reported.getPylonHVBmu() : null);

        ConfigurationDifference.add(differences, "pylonHVCmu",
                desired != null ? desired.getPylonHVCmu() : null,
                reported != null ? reported.getPylonHVCmu() : null);

        return differences;
    }
}
