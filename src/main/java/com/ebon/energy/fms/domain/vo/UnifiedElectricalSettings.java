package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.domain.vo.Capacity;
import com.ebon.energy.fms.domain.vo.Volt;
import com.ebon.energy.fms.domain.vo.WattHour;
import com.ebon.energy.fms.domain.vo.site.GenericInverterConfiguration;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 统一电气设置类
 */
public class UnifiedElectricalSettings {
    private static int NOMINAL_BATTERY_VOLTAGE_V = 48;

    private BigDecimal powerFactor;
    private Boolean shadowScanEnabled;
    private String safetyCountry;
    private GenericInverterConfiguration conf;
    private String batteryManufacturer;
    private WattHour batteryCapacityWh;

    public UnifiedElectricalSettings(
            BigDecimal powerFactor,
            Boolean shadowScanEnabled,
            String safetyCountry,
            GenericInverterConfiguration conf,
            String batteryManufacturer,
            WattHour batteryCapacityWh) {
        this.powerFactor = powerFactor;
        this.shadowScanEnabled = shadowScanEnabled;
        this.safetyCountry = safetyCountry;
        this.conf = conf;
        this.batteryManufacturer = batteryManufacturer;
        this.batteryCapacityWh = batteryCapacityWh;
    }

    public BigDecimal getPowerFactor() {
        return powerFactor;
    }

    public Boolean isShadowScanEnabled() {
        return shadowScanEnabled;
    }

    public String getSafetyCountry() {
        return safetyCountry;
    }

    public GenericInverterConfiguration getConf() {
        return conf;
    }

    public String getBatteryManufacturer() {
        return batteryManufacturer;
    }

    public WattHour getBatteryCapacityWh() {
        return batteryCapacityWh;
    }

    /**
     * 从总容量（安时）计算电池容量（瓦时）
     * @param totalCapacityAh 总容量（安时，可选）
     * @return 电池容量（瓦时，可选）
     */
    public static WattHour calculateBatteryCapacityFrom(Integer totalCapacityAh) {
        if (totalCapacityAh == null) {
            return null;
        }
        // 单位转换：Ah * V = Wh
        Capacity capacityAh = new Capacity(totalCapacityAh);
        Volt nominalVoltage = new Volt(NOMINAL_BATTERY_VOLTAGE_V);
        return new WattHour(capacityAh.getValue().multiply(nominalVoltage.getValue()));
    }
}