package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.GoodWeSiteExportLimitType;
import com.ebon.energy.fms.common.enums.GoodWeSiteGenerationLimitType;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.InverterModeValue;
import com.ebon.energy.fms.domain.vo.product.control.report.InverterFirmwareDesiredSettings;
import com.ebon.energy.fms.domain.vo.product.control.report.InverterRelayCheckDesiredSettings;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.ManagedInverterSettingDesired;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Data
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterDesiredSettingsVO {
    public static String GEN_LIMIT_CONTROL_TYPE_NAME = "GenLimitControlType";
    public static String GEN_LIMIT_CONTROL_SOFT_LIM_VA_NAME = "GenLimitControlSoftLimVA";
    public static String GEN_LIMIT_CONTROL_HARD_LIM_VA_NAME = "GenLimitControlHardLimVA";
    public static String BATTERY_SETTINGS_NAME = "battery";
    public static String FIRMWARE_SETTINGS_NAME = "firmware";
    public static String MANAGED_SETTINGS_NAME = "managedSettings";
    public static String ARBITRARY_READER_SETTINGS_NAME = "arbitraryReaderSettings";
    public static String WORK_MODE_NAME = "workMode";
    public static String WORK_MODE_POWER_W_NAME = "workModePowerW";

    private BatteryDesiredSettingsVO battery;
    private InverterFirmwareDesiredSettings firmware;
    private Map<String, ManagedInverterSettingDesired> managedSettings;
    private Map<String, ArbitraryReaderSettingsDesiredVO> arbitraryReaderSettings;
    private Boolean DisableManagedSettings;
    private String ModelName;
    private Integer SafetyCountry;
    private Boolean IsShadowScanEnabled;
    private InverterModeValue workMode;
    private Double workModePowerW;
    private Watt SiteExportLimitW;
    private Boolean DoesSiteRequireExportLimit;
    private GoodWeSiteExportLimitType SiteExportLimitType;
    private BigDecimal PowerFactor;
    private Short BackupStartDelay;
    private Short RecoverTimeEE;
    private String ArbitrarySettingRegister;
    private String ArbitrarySettingValue;
    private String ArbitrarySettingToken;
    private String ArbitrarySettingProtocol;
    private Boolean DisablePowerModeManager;
    private Boolean DisableBMSProcessor;
    private UUID GridProfileId;
    private String GridProfileCorrelationId;
    private InverterRelayCheckDesiredSettings relayCheck;
    private GoodWeSiteGenerationLimitType GenLimitControlType;
    private VoltAmps GenLimitControlSoftLimVA;
    private VoltAmps GenLimitControlHardLimVA;

    public InverterDesiredSettingsVO(
            BatteryDesiredSettingsVO battery,
            InverterFirmwareDesiredSettings firmware,
            Map<String, ManagedInverterSettingDesired> managedSettings,
            Map<String, ArbitraryReaderSettingsDesiredVO> arbitraryReaderSettings,
            Boolean disableManagedSettings,
            String modelName,
            Integer safetyCountry,
            Boolean isShadowScanEnabled,
            InverterModeValue workMode,
            Double workModePowerW,
            Watt siteExportLimitW,
            Boolean doesSiteRequireExportLimit,
            GoodWeSiteExportLimitType siteExportLimitType,
            BigDecimal powerFactor,
            Short backupStartDelay,
            Short recoverTimeEE,
            String arbitrarySettingRegister,
            String arbitrarySettingValue,
            String arbitrarySettingToken,
            String arbitrarySettingProtocol,
            Boolean disablePowerModeManager,
            Boolean disableBMSProcessor,
            UUID gridProfileId,
            String gridProfileCorrelationId,
            InverterRelayCheckDesiredSettings relayCheckSettings,
            GoodWeSiteGenerationLimitType genLimitControlType,
            VoltAmps genLimitControlSoftLimVA,
            VoltAmps genLimitControlHardLimVA) {

        this.battery = battery;
        this.firmware = firmware;
        this.managedSettings = managedSettings != null ? new HashMap<>(managedSettings) : new HashMap<>();
        this.arbitraryReaderSettings = arbitraryReaderSettings != null ? new HashMap<>(arbitraryReaderSettings) : new HashMap<>();
        this.DisableManagedSettings = disableManagedSettings;
        this.ModelName = modelName;
        this.SafetyCountry = safetyCountry;
        this.IsShadowScanEnabled = isShadowScanEnabled;
        this.workMode = workMode;
        this.workModePowerW = workModePowerW;
        this.SiteExportLimitW = siteExportLimitW;
        this.DoesSiteRequireExportLimit = doesSiteRequireExportLimit;
        this.SiteExportLimitType = siteExportLimitType;
        this.PowerFactor = powerFactor;
        this.BackupStartDelay = backupStartDelay;
        this.RecoverTimeEE = recoverTimeEE;
        this.ArbitrarySettingRegister = arbitrarySettingRegister;
        this.ArbitrarySettingValue = arbitrarySettingValue;
        this.ArbitrarySettingToken = arbitrarySettingToken;
        this.ArbitrarySettingProtocol = arbitrarySettingProtocol;
        this.DisablePowerModeManager = disablePowerModeManager;
        this.DisableBMSProcessor = disableBMSProcessor;
        this.GridProfileId = gridProfileId;
        this.GridProfileCorrelationId = gridProfileCorrelationId;
        this.relayCheck = relayCheckSettings;
        this.GenLimitControlType = genLimitControlType;
        this.GenLimitControlSoftLimVA = genLimitControlSoftLimVA;
        this.GenLimitControlHardLimVA = genLimitControlHardLimVA;
    }

    public static InverterDesiredSettingsVO getDefault() {
        return new InverterDesiredSettingsVO(
                BatteryDesiredSettingsVO.getDefault(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                new InverterRelayCheckDesiredSettings(),
                null,
                null,
                null);
    }

    // Builder methods
    public InverterDesiredSettingsVO withBatterySettings(BatteryDesiredSettingsVO batterySettings) {
        return new InverterDesiredSettingsVO(
                batterySettings, firmware, managedSettings, arbitraryReaderSettings,
                DisableManagedSettings, ModelName, SafetyCountry, IsShadowScanEnabled,
                workMode, workModePowerW, SiteExportLimitW, DoesSiteRequireExportLimit,
                SiteExportLimitType, PowerFactor, BackupStartDelay, RecoverTimeEE,
                ArbitrarySettingRegister, ArbitrarySettingValue, ArbitrarySettingToken,
                ArbitrarySettingProtocol, DisablePowerModeManager, DisableBMSProcessor,
                GridProfileId, GridProfileCorrelationId, relayCheck,
                GenLimitControlType, GenLimitControlSoftLimVA, GenLimitControlHardLimVA);
    }

    // ... other builder methods ...

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InverterDesiredSettingsVO that = (InverterDesiredSettingsVO) o;
        return Objects.equals(battery, that.battery) &&
                Objects.equals(firmware, that.firmware) &&
                managedSettings.equals(that.managedSettings) &&
                arbitraryReaderSettings.equals(that.arbitraryReaderSettings) &&
                Objects.equals(DisableManagedSettings, that.DisableManagedSettings) &&
                Objects.equals(ModelName, that.ModelName) &&
                Objects.equals(SafetyCountry, that.SafetyCountry) &&
                Objects.equals(IsShadowScanEnabled, that.IsShadowScanEnabled) &&
                workMode == that.workMode &&
                Objects.equals(workModePowerW, that.workModePowerW) &&
                Objects.equals(SiteExportLimitW, that.SiteExportLimitW) &&
                Objects.equals(DoesSiteRequireExportLimit, that.DoesSiteRequireExportLimit) &&
                SiteExportLimitType == that.SiteExportLimitType &&
                Objects.equals(PowerFactor, that.PowerFactor) &&
                Objects.equals(BackupStartDelay, that.BackupStartDelay) &&
                Objects.equals(RecoverTimeEE, that.RecoverTimeEE) &&
                Objects.equals(ArbitrarySettingRegister, that.ArbitrarySettingRegister) &&
                Objects.equals(ArbitrarySettingValue, that.ArbitrarySettingValue) &&
                Objects.equals(ArbitrarySettingToken, that.ArbitrarySettingToken) &&
                Objects.equals(ArbitrarySettingProtocol, that.ArbitrarySettingProtocol) &&
                Objects.equals(DisablePowerModeManager, that.DisablePowerModeManager) &&
                Objects.equals(DisableBMSProcessor, that.DisableBMSProcessor) &&
                Objects.equals(GridProfileId, that.GridProfileId) &&
                Objects.equals(GridProfileCorrelationId, that.GridProfileCorrelationId) &&
                Objects.equals(relayCheck, that.relayCheck) &&
                GenLimitControlType == that.GenLimitControlType &&
                Objects.equals(GenLimitControlSoftLimVA, that.GenLimitControlSoftLimVA) &&
                Objects.equals(GenLimitControlHardLimVA, that.GenLimitControlHardLimVA);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                battery, firmware, managedSettings, arbitraryReaderSettings,
                DisableManagedSettings, ModelName, SafetyCountry, IsShadowScanEnabled,
                workMode, workModePowerW, SiteExportLimitW, DoesSiteRequireExportLimit,
                SiteExportLimitType, PowerFactor, BackupStartDelay, RecoverTimeEE,
                ArbitrarySettingRegister, ArbitrarySettingValue, ArbitrarySettingToken,
                ArbitrarySettingProtocol, DisablePowerModeManager, DisableBMSProcessor,
                GridProfileId, GridProfileCorrelationId, relayCheck,
                GenLimitControlType, GenLimitControlSoftLimVA, GenLimitControlHardLimVA);
    }
}