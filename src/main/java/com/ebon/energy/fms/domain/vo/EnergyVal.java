package com.ebon.energy.fms.domain.vo;

import lombok.Data;

@Data
public class EnergyVal {

    public EnergyVal(WattHour wh, Boolean isAccurate) {
        this.wh = wh;
        this.isAccurate = isAccurate;
    }

    public EnergyVal(Integer wh, Boolean isAccurate) {
        this.wh = wh != null ? new WattHour(wh) : null;
        this.isAccurate = isAccurate;
    }

    public WattHour wh;

    public Boolean isAccurate;
}
