package com.ebon.energy.fms.domain.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ProductDailyHistoryDO {

    private String serialNumber;

    private String date;

    private Timestamp dateDate;

    private int dailyUsageWh;

    private int dailySoldWh;

    private int dailyBoughtWh;

    private int dailyGenerationWh;

    public Integer dailyBatteryChargedWh;

    public Integer dailyBatteryDischargedWh;

    public Boolean dailyUsageAdjusted;

    public Boolean dailySoldAdjusted;

    public Boolean dailyBoughtAdjusted;

    public Boolean dailyGenerationAdjusted;

    public Boolean dailyBatteryChargedAdjusted;

    public Boolean dailyBatteryDischargedAdjusted;
    
}
