package com.ebon.energy.fms.domain.vo.site;

import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;

import java.time.LocalDateTime;
import java.util.Optional;

public class SiteCoordinatorSiteData {
    private String serialNumber;
    private Boolean isConnected;
    private Version rossVersion;
    private String model;
    private LocalDateTime manufactureDate;
    private Integer manufactureSerialNumber; // 可从SerialNumber推导
    private String siteRole;
    private Boolean isGridTie;
    private String siteId;

    // 构造方法
    public SiteCoordinatorSiteData() {
        // 默认构造函数
    }

    // Getters and Setters
    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Boolean getIsConnected() {
        return isConnected;
    }

    public void setIsConnected(Boolean connected) {
        isConnected = connected;
    }

    public Version getRossVersion() {
        return rossVersion;
    }

    public void setRossVersion(Version rossVersion) {
        this.rossVersion = rossVersion;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public LocalDateTime getManufactureDate() {
        return manufactureDate;
    }

    public void setManufactureDate(LocalDateTime manufactureDate) {
        this.manufactureDate = manufactureDate;
    }

    public Integer getManufactureSerialNumber() {
        return manufactureSerialNumber;
    }

    public void setManufactureSerialNumber(Integer manufactureSerialNumber) {
        this.manufactureSerialNumber = manufactureSerialNumber;
    }

    public String getSiteRole() {
        return siteRole;
    }

    public void setSiteRole(String siteRole) {
        this.siteRole = siteRole;
    }

    public Boolean getIsGridTie() {
        return isGridTie;
    }

    public void setGridTie(boolean gridTie) {
        isGridTie = gridTie;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }
}