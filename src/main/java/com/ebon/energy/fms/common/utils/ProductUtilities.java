package com.ebon.energy.fms.common.utils;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;

/**
 * Utility class for product-related operations
 */
public class ProductUtilities {
    
    // Define the time threshold for considering a product online (15 minutes)
    private static final long ONLINE_THRESHOLD_MINUTES = 15;
    
    /**
     * Determine if a product is online based on its last system status received time
     * 
     * @param lastSystemStatusReceived The last time a system status was received
     * @return True if the product is considered online, false otherwise
     */
    public static boolean isOnline(LocalDateTime lastSystemStatusReceived) {
        if (lastSystemStatusReceived == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(lastSystemStatusReceived, now);
        
        return duration.toMinutes() <= ONLINE_THRESHOLD_MINUTES;
    }

    public static boolean isOnline(Timestamp lastReceived, long millisecond) {
        if (lastReceived != null && (System.currentTimeMillis() - lastReceived.getTime() <= millisecond)) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isOnline(ZonedDateTime lastReceived, long millisecond) {
        if (lastReceived != null && (Instant.now().toEpochMilli() - lastReceived.toInstant().toEpochMilli() <= millisecond)) {
            return true;
        } else {
            return false;
        }
    }
    
}
