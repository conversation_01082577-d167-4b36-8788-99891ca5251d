package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.domain.vo.Ampere;
import com.ebon.energy.fms.domain.vo.ElectricalConfigurationVO;
import com.ebon.energy.fms.domain.vo.Volt;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.site.GenericInverterConfiguration;

import java.math.BigDecimal;
import java.util.Optional;

public class GenericInverterConfigurationHelper {
    public static GenericInverterConfiguration fromElectricalConfiguration(ElectricalConfigurationVO conf) {
        // 计算最小SOC（百分比转小数）
        BigDecimal minSoC0to1 = Optional.ofNullable(conf)
                .map(ElectricalConfigurationVO::getMinimumSoC)
                .map(soc -> BigDecimal.valueOf(soc).divide(BigDecimal.valueOf(100)))
                .orElse(BigDecimal.ZERO);

        // 计算离网最小SOC（百分比转小数）
        BigDecimal minSoCOffGrid0to1 = Optional.ofNullable(conf)
                .map(ElectricalConfigurationVO::getMinimumSoCOffGrid)
                .map(soc -> BigDecimal.valueOf(soc).divide(BigDecimal.valueOf(100)))
                .orElse(BigDecimal.ZERO);

        // 根据BMS检测状态选择最大放电电流
        Double maxDischargeCurrent = Optional.ofNullable(conf)
                .filter(c -> c.getBMSDetected() != null && c.getBMSDetected())
                .map(c -> c.getBatteryDischargeSettingsAuto() != null ?
                        c.getBatteryDischargeSettingsAuto().getCurrentValue() : null)
                .orElseGet(() -> Optional.ofNullable(conf)
                        .map(c -> c.getBatteryDischargeSettingsCustom() != null ?
                                c.getBatteryDischargeSettingsCustom().getCurrentValue() : null)
                        .orElse(null));

        // 根据BMS检测状态选择最大充电电流
        Double maxChargeCurrent = Optional.ofNullable(conf)
                .filter(c -> c.getBMSDetected() != null && c.getBMSDetected())
                .map(c -> c.getBatteryChargeSettingsAuto() != null ?
                        c.getBatteryChargeSettingsAuto().getCurrentValue() : null)
                .orElseGet(() -> Optional.ofNullable(conf)
                        .map(c -> c.getBatteryChargeSettingsCustom() != null ?
                                c.getBatteryChargeSettingsCustom().getCurrentValue() : null)
                        .orElse(null));

        // 根据BMS检测状态选择最大充电电压
        Double maxChargeVoltage = Optional.ofNullable(conf)
                .filter(c -> c.getBMSDetected() != null && c.getBMSDetected())
                .map(c -> c.getBatteryChargeSettingsAuto() != null ?
                        c.getBatteryChargeSettingsAuto().getVoltageValue() : null)
                .orElseGet(() -> Optional.ofNullable(conf)
                        .map(c -> c.getBatteryChargeSettingsCustom() != null ?
                                c.getBatteryChargeSettingsCustom().getVoltageValue() : null)
                        .orElse(null));

        // 获取功能站点出口限制
        Watt functionalSiteExportLimit = Optional.ofNullable(conf)
                .map(GenericInverterConfigurationHelper::getFunctionalSiteExportLimit)
                .orElse(null);

        // 判断是否有电池
        boolean hasBatteries = Optional.ofNullable(conf)
                .map(c -> c.getBatteryType() != null && c.getBatteryType().ordinal() != 0)
                .orElse(false);

        return new GenericInverterConfiguration(
                minSoC0to1,
                minSoCOffGrid0to1,
                maxDischargeCurrent != null ? new Ampere(maxDischargeCurrent) : null,
                maxChargeCurrent != null ? new Ampere((maxChargeCurrent)) : null,
                maxChargeVoltage != null ? new Volt(maxChargeVoltage) : null,
                functionalSiteExportLimit,
                hasBatteries
        );
    }

    public static Watt getFunctionalSiteExportLimit(ElectricalConfigurationVO e) {
        return Optional.ofNullable(e)
                .filter(config -> config.getIsLimitExportPower() != null &&
                        config.getIsLimitExportPower() &&
                        getSiteExportLimitSetting(config) != null)
                .map(GenericInverterConfigurationHelper::getSiteExportLimitSetting)
                .orElse(null);
    }

    public static Watt getSiteExportLimitSetting(ElectricalConfigurationVO configuration) {
        return Optional.ofNullable(configuration)
                .map(config -> {
                    // 优先级：SiteLimit > LimitExportPower > LimitExportPowerUserValue
                    if (config.getSiteLimit() != null) {
                        return config.getSiteLimit();
                    } else if (config.getLimitExportPower() != null) {
                        return config.getLimitExportPower();
                    } else {
                        return config.getLimitExportPowerUserValue();
                    }
                })
                .map(value -> new Watt(value)) // 假设Watt有接收BigDecimal的构造函数
                .orElse(null);
    }
}
