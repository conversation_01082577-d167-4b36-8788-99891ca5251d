package com.ebon.energy.fms.common.enums;

import com.ebon.energy.fms.domain.vo.PhaseRoleVO;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PhaseRole {

    Standalone("Standalone"),

    Coordinator("Master"),

    Worker("Worker"),

    Incompatible("Incompatible"),

    GridTie("Grid Tie");

    private final String displayName;

    PhaseRole(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static String getDisplayNameFromName(String name) {
        for (PhaseRole e : values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e.getDisplayName();
            }
        }

        return name;
    }

    public static List<PhaseRoleVO> getPhaseRoles() {
        return Arrays.stream(values()).map(e -> {
            PhaseRoleVO phaseRoleVO = new PhaseRoleVO();
            phaseRoleVO.setValue(e.name());
            phaseRoleVO.setDisplayName(e.getDisplayName());
            return phaseRoleVO;
        }).collect(Collectors.toList());
    }

}