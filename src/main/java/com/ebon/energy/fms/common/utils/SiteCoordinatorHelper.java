package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;
import com.ebon.energy.fms.domain.vo.site.DecideCoordinatorDto;
import com.ebon.energy.fms.domain.vo.site.SiteCoordinatorDisplayNames;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class SiteCoordinatorHelper {

    private static final SiteCoordinatorDisplayNames siteCoordinatorDisplayNames = new SiteCoordinatorDisplayNames();

    /**
     * 判断设备是否可以作为协调器或工作器（考虑版本）
     */
    public static boolean canBeCoordinatorOrWorker(boolean isConnected, Version rossVersion, boolean isGridTie) {
        return new RossVersion(rossVersion).supportsSiteCoordinator()
                && canBeCoordinatorOrWorker(isConnected, isGridTie);
    }

    /**
     * 判断设备是否可以作为协调器或工作器（不考虑版本）
     */
    public static boolean canBeCoordinatorOrWorker(boolean isConnected, boolean isGridTie) {
        return canBeACoordinator(isConnected, isGridTie) || canBeAWorker(isGridTie);
    }

    private static boolean canBeACoordinator(boolean isConnected, boolean isGridTie) {
        return isConnected && !isGridTie;
    }

    private static boolean canBeAWorker(boolean isGridTie) {
        return !isGridTie;
    }

    /**
     * 决定协调器设备
     */
    public static String decideCoordinator(List<DecideCoordinatorDto> siteData) {
        // 检查是否已有协调器
        List<DecideCoordinatorDto> coordinators = siteData.stream()
                .filter(p -> p.getSiteRole().equals(PhaseRole.Coordinator.name()))
                .collect(Collectors.toList());

        if (!coordinators.isEmpty()) {
            if (coordinators.size() > 1) {
                throw new IllegalStateException(
                        "There can be only one " + siteCoordinatorDisplayNames.getDisplayName(PhaseRole.Coordinator.name()) + " per site");
            }
            return coordinators.get(0).getRossSerialNumber();
        }

        // 查找符合条件的协调器候选
        Optional<DecideCoordinatorDto> candidate = siteData.stream()
                .filter(p -> canBeCoordinatorOrWorker(p.getIsConnected(), p.getRossVersion(), p.getIsGridTie()))
                .sorted(Comparator.comparing(DecideCoordinatorDto::getManufactureDate)
                        .thenComparing(DecideCoordinatorDto::getManufactureSerialNumber))
                .findFirst();

        if (!candidate.isPresent()) {
            candidate = siteData.stream()
                    .filter(p -> canBeCoordinatorOrWorker(p.getIsConnected(), p.getIsGridTie()))
                    .sorted(Comparator.comparing(DecideCoordinatorDto::getManufactureDate)
                            .thenComparing(DecideCoordinatorDto::getManufactureSerialNumber))
                    .findFirst();
        }

        return candidate.map(DecideCoordinatorDto::getRossSerialNumber).orElse("");
    }

    /**
     * 创建主机名
     */
    public static String createHostName(String serialNumber, boolean isGen3) {
        if (isGen3) {
            return serialNumber + ".local";
        } else {
            if (serialNumber.startsWith("RB") && serialNumber.length() == 16) {
                String part2 = serialNumber.substring(2, 8);   // 2到7共6位
                String part3 = serialNumber.substring(12, 16); // 12到15共4位
                return "REDB" + part2 + part3 + ".local";
            }
        }
        return "";
    }

}
