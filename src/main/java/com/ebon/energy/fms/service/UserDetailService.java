package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ebon.energy.fms.domain.entity.RedbackUserDetailsDO;
import com.ebon.energy.fms.mapper.third.RedbackUserDetailsMapper;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class UserDetailService {

    @Resource
    private RedbackUserDetailsMapper userDetailsMapper;

    public List<RedbackUserDetailsDO> getUserDetails(List<String> sns, String name) {
        if (StringUtils.isBlank(RequestUtil.getPortolUserId())) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<RedbackUserDetailsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RedbackUserDetailsDO::getRedbackProductSn, sns);
        queryWrapper.eq(RedbackUserDetailsDO::getName, name);
        queryWrapper.eq(RedbackUserDetailsDO::getRedbackUserId, RequestUtil.getPortolUserId());
        return userDetailsMapper.selectList(queryWrapper);
    }

    public int update(RedbackUserDetailsDO detailsDO) {
        LambdaUpdateWrapper<RedbackUserDetailsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(RedbackUserDetailsDO::getValue, detailsDO.getValue());
        updateWrapper.eq(RedbackUserDetailsDO::getRedbackProductSn, detailsDO.getRedbackProductSn());
        updateWrapper.eq(RedbackUserDetailsDO::getName, detailsDO.getName());
        updateWrapper.eq(RedbackUserDetailsDO::getRedbackUserId, detailsDO.getRedbackUserId());
        return userDetailsMapper.update(null, updateWrapper);
    }

}