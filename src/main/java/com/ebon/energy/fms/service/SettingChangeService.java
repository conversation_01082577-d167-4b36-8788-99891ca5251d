package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SettingsReaderProvider;
import com.ebon.energy.fms.domain.vo.setting.LogicalSettingsChangeRequestDto;
import com.ebon.energy.fms.domain.vo.setting.provider.SettingsBuilderProvider;
import com.ebon.energy.fms.repository.impl.SettingsServiceImpl;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SettingChangeService {

    private final SettingsServiceImpl settingsService;


    public void apply(String serialNumber, String changeRequestId, List<LogicalSettingsChangeRequestDto> changeRequest, boolean updateDeviceSettings) {
        var settingsDtoAsync = settingsService.getSettingsDtoAsync(serialNumber);
        //todo update
        var reader = SettingsReaderProvider.get(settingsDtoAsync);
        var builder = SettingsBuilderProvider.get(settingsDtoAsync);
//        ensureDeviceHasTimezoneSet(serialNumber, reader, builder, changeRequest);
//        ensureDeviceHasDefaultLimitsSet(serialNumber, reader, builder, changeRequest);

        for (var settingChange : changeRequest) {

            if (settingChange.getTargetSetting() == )
        }


        if (batterySettings == null) {
            throw new BizException("batterySettings must not be null");
        }

        builder.addBatterySettings(
                batterySettings.getManufacturer(),
                batterySettings.getBatteryCount(),
                batterySettings.getMaxChargeCurrent(),
                batterySettings.getMaxDischargeCurrent(),
                batterySettings.getMinSoc(),
                batterySettings.getMinOffgridSoc()
        );

        var patch = builder.toPatchString();

        settingsService.updateDesiredDeviceSettingsFromJsonPatch(RequestUtil.getPortolUserId(), serialNumber, patch.getJson(), patch.getIntent(), true);

    }

}
