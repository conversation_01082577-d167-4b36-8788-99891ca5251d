package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SiteCoordinatorHelper;
import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.po.SiteInverterAddPO;
import com.ebon.energy.fms.domain.po.SiteInverterUpdatePO;
import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.SiteInverterInfoVO;
import com.ebon.energy.fms.domain.vo.SiteVO;
import com.ebon.energy.fms.domain.vo.TelemetryDataVO;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;
import com.ebon.energy.fms.domain.vo.site.DecideCoordinatorDto;
import com.ebon.energy.fms.domain.vo.site.SetSiteCoordinatorDataInDto;
import com.ebon.energy.fms.domain.vo.site.SiteCoordinatorSiteData;
import com.ebon.energy.fms.domain.vo.telemetry.OuijaBoardTelemetry;
import com.ebon.energy.fms.domain.vo.telemetry.RossDeviceTelemetry;
import com.ebon.energy.fms.domain.vo.telemetry.RossTelemetry;
import com.ebon.energy.fms.domain.vo.telemetry.TelemetryExtensions;
import com.ebon.energy.fms.mapper.third.*;
import com.ebon.energy.fms.service.factory.TableStoreServiceFactory;
import com.ebon.energy.fms.util.*;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.common.constants.GlobalConstants.ROLE_MASTER;
import static com.ebon.energy.fms.common.utils.ProductUtilities.isOnline;
import static com.ebon.energy.fms.util.SafeAccess.getValue;
import static com.ebon.energy.fms.util.StreamUtil.mapList;

@Slf4j
@Service
public class SiteService {

    @Resource
    private SiteMapper siteMapper;

    @Resource
    private ProductMapper productMapper;
    
    @Resource
    private ProductInstallationMapper productInstallationMapper;

    @Resource
    private MetricsTelemetryMapper metricsTelemetryMapper;
    
    @Resource
    private HardwareModelMapper hardwareModelMapper;

    @Resource
    private AddressService addressService;

    @Resource
    private UserDetailService userDetailService;
    
    @Resource
    private SiteDeviceService siteDeviceService;

    @Resource
    private TableStoreServiceFactory tableStoreServiceFactory;

    public SiteDO getBySystemId(String systemId) {
        LambdaQueryWrapper<SiteDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteDO::getSystemId, systemId);
        return siteMapper.selectOne(queryWrapper);
    }

    public PageResult<SiteVO> getSiteList(SiteListPO po) {
        Page<SiteDO> page = new Page<>(po.getCurrent(), po.getPageSize());
        LambdaQueryWrapper<SiteDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNoneBlank(po.getSiteId())) {
            queryWrapper.eq(SiteDO::getSystemId, po.getSiteId());
        }
        Page<SiteDO> sitePage = siteMapper.selectPage(page, queryWrapper);
        if (sitePage.getTotal() == 0) {
            return PageResult.toResponse(Collections.EMPTY_LIST, sitePage.getTotal(), po.getCurrent(), po.getPageSize());
        }

        List<String> systemIds = mapList(sitePage.getRecords(), SiteDO::getSystemId);
        List<SiteDeviceDO> siteDevices = siteMapper.selectSiteDevicesBatch(systemIds);
        List<ProductWithOwnerDO> productWithOwners = CollectionUtils.isNotEmpty(siteDevices) ? productMapper.selectWithOwnerBatch(mapList(siteDevices, SiteDeviceDO::getSerialNumber))
                : Collections.EMPTY_LIST;
        Map<String, ProductWithOwnerDO> productWithOwnerMap = StreamUtil.toMap(productWithOwners, ProductWithOwnerDO::getSerialNumber);
        Map<String, List<SiteDeviceDO>> siteDeviceGroup = StreamUtil.groupBy(siteDevices, SiteDeviceDO::getSiteId);

        List<AddressesDO> addressList = addressService.getByIds(mapList(sitePage.getRecords(), SiteDO::getAddressId));
        Map<Integer, AddressesDO> addressMap = StreamUtil.toMap(addressList, AddressesDO::getId);

        List<SiteVO> list = sitePage.getRecords().stream().map(e -> {
            SiteVO vo = new SiteVO();
            vo.setSiteId(e.getSystemId());
            AddressesDO addressesDO = addressMap.get(e.getAddressId());
            if (addressesDO != null) {
                vo.setAddress(AddressFormatter.formatAddress(addressesDO));
            }

            List<SiteDeviceDO> devices = siteDeviceGroup.get(e.getSystemId());
            if (CollectionUtils.isNotEmpty(devices)) {
                ProductWithOwnerDO productWithOwnerDO = productWithOwnerMap.get(devices.get(0).getSerialNumber());
                if (productWithOwnerDO != null) {
                    vo.setOwnerName(Strings.nullToEmpty(productWithOwnerDO.getOwnerFirstName()) + " " + Strings.nullToEmpty(productWithOwnerDO.getOwnerLastName()));
                    vo.setOwnerName(vo.getOwnerName().trim());
                }
            }

            return vo;
        }).collect(Collectors.toList());

        return PageResult.toResponse(list, sitePage.getTotal(), po.getCurrent(), po.getPageSize());
    }

    public List<SiteInverterInfoVO> getSiteInverterList(String siteId) {
        List<SiteInverterInfoDO> inverters = productMapper.selectSiteInverters(siteId);
        if (CollectionUtils.isEmpty(inverters)) {
            return Collections.EMPTY_LIST;
        }

        List<HardwareModelAndFamilyDO> hardwareModels = hardwareModelMapper.selectWithFamily();
        Map<String, HardwareModelAndFamilyDO> hardwareModelMap = hardwareModels.stream().collect(Collectors.toMap(HardwareModelAndFamilyDO::getName, Function.identity(), (first, second) -> first));

        List<String> sns = mapList(inverters, SiteInverterInfoDO::getSerialNumber);
        List<RedbackUserDetailsDO> userDetails = userDetailService.getUserDetails(sns, UserPreferenceKey.ProductFriendlyName.name());
        Map<String, String> userDetailMap = StreamUtil.toMap(userDetails, RedbackUserDetailsDO::getRedbackProductSn, RedbackUserDetailsDO::getValue);

        List<MetricsTelemetryDO> metricsList = metricsTelemetryMapper.selectInSns(sns);
        Map<String, MetricsTelemetryDO> metricsMap = metricsList.stream().collect(Collectors.toMap(MetricsTelemetryDO::getSerialNumber, Function.identity(), (first, second) -> first));
        List<SiteInverterInfoVO> inverterList = inverters.stream().map(e -> {
            SiteInverterInfoVO inverterVO = new SiteInverterInfoVO();

            MetricsTelemetryDO telemetryDO = metricsMap.get(e.getSerialNumber());
            boolean hasError = telemetryDO.getIncidentsForHomeUser() > 0 || telemetryDO.getIncidentsForInstaller() > 0 || telemetryDO.getIncidentsForRb() > 0;

            inverterVO.setInverterName(userDetailMap.get(e.getSerialNumber()));
            inverterVO.setCurrentRole(PhaseRole.getDisplayNameFromName(decideCurrentRole(e.getCurrentRole(), e.getSelectedRole(),
                    new RossVersion(e.getRossVersion()).getRossVersionNumber(), e.getModel())));
            inverterVO.setSerialNumber(e.getSerialNumber());
            inverterVO.setMeter(e.getMeter() != null && "TRUE".equalsIgnoreCase(e.getMeter()) ? "Connected" : "");

            String modelName = e.getModel();
            HardwareModelAndFamilyDO hardwareModelDO = modelName != null ? hardwareModelMap.get(modelName) : null;
            inverterVO.setModel(Strings.nullToEmpty(hardwareModelDO != null ? hardwareModelDO.getDisplayName() : modelName));

            inverterVO.setSelectedRole(PhaseRole.getDisplayNameFromName(e.getSelectedRole()));
            inverterVO.setStatus(InverterStatusHelper.makeInverterStatus(
                    e.isInWarranty(),
                    e.isOffComms(),
                    e.isRegistered(),
                    e.isPending(),
                    hasError,
                    e.isOnline()));
            inverterVO.setRossVersion(e.getRossVersion());
            inverterVO.setFirmwareVersion(e.getFirmwareVersion());
            inverterVO.setSiteId(e.getPublicSiteId());
            inverterVO.setComputerName(e.getComputerName());
            inverterVO.setLatestTelemetryUtc(TimeUtils.toZonedDateTime(e.getLatestTelemetryUtc()));
            inverterVO.setIsHourlyOnline(isOnline(e.getLatestTelemetryUtc(), 3600000L));
            inverterVO.setIsDailyOnline(isOnline(e.getLatestTelemetryUtc(), 86400000L));

            setComputerName(inverterVO);

            return inverterVO;
        }).collect(Collectors.toList());

        return sortResults(inverterList);
    }

    public void updateInverterSelectedRole(SiteInverterUpdatePO updatePO) {
        String siteId = siteMapper.selectSiteId(updatePO.getSerialNumber());
        List<SiteInverterInfoDO> inverters = productMapper.selectSiteInverters(siteId);
        if (CollectionUtils.isEmpty(inverters)) {
            throw new BizException("There are no device");
        }

        SiteInverterInfoDO inverterInfoDO = inverters.stream().filter(e -> e.getSerialNumber().equals(updatePO.getSerialNumber())).findFirst().orElse(null);
        inverterInfoDO.setSelectedRole(updatePO.getSelectedRole());

        if (!updatePO.getSelectedRole().equalsIgnoreCase(PhaseRole.Coordinator.name())) {
            long count = inverters.stream().filter(e -> e.getSelectedRole().equalsIgnoreCase(PhaseRole.Coordinator.name())).count();
            if (count != 1) {
                throw new BizException("There must be one " + PhaseRole.Coordinator.name() + ". There are currently " + count + ".");
            }
        }

        // 筛选出所有角色为Worker且不支持Site Coordinator的设备
        List<SiteInverterInfoDO> unsupportedWorkers = inverters.stream()
                .filter(d -> {
                    RossVersion rossVersion = new RossVersion(d.getRossVersion());
                    return PhaseRole.Worker.name().equals(d.getSelectedRole())
                            && !rossVersion.supportsSiteCoordinator();
                })
                .collect(Collectors.toList());

        // 如果存在不支持的Worker设备
        if (CollectionUtils.isNotEmpty(unsupportedWorkers)) {
            // 检查是否同时存在支持的Worker设备
            boolean hasSupportedWorkers = inverters.stream()
                    .anyMatch(d -> {
                        RossVersion rossVersion = new RossVersion(d.getRossVersion());
                        return PhaseRole.Worker.name().equals(d.getSelectedRole())
                                && rossVersion.supportsSiteCoordinator();
                    });
            if (!hasSupportedWorkers) {
                throw new BizException("All devices entering a Master/Worker relationship, must first be updated to a version that supports site manager.");
            }
        }

        if (StringUtils.isNotBlank(updatePO.getInverterName())) {
            //更新逆变器名称
            RedbackUserDetailsDO detailsDO = new RedbackUserDetailsDO();
            detailsDO.setRedbackUserId(RequestUtil.getPortolUserId());
            detailsDO.setRedbackProductSn(updatePO.getSerialNumber());
            detailsDO.setName(UserPreferenceKey.ProductFriendlyName.name());
            detailsDO.setValue(updatePO.getInverterName());
            userDetailService.update(detailsDO);
        }
        
        productInstallationMapper.updatePhaseRole(updatePO.getSerialNumber(), updatePO.getSelectedRole());

        siteDeviceService.updateSettingsAndNotify(inverters);
    }
    
    public void addInverter(SiteInverterAddPO addPO){
        productInstallationMapper.mergeProductToSite(addPO.getSiteId(), addPO.getSerialNumber());
        setSiteRoleBySerialNumber(addPO.getSerialNumber());

        String siteId = siteMapper.selectSiteId(addPO.getSerialNumber());
        List<SiteInverterInfoDO> inverters = productMapper.selectSiteInverters(siteId);
        if (CollectionUtils.isEmpty(inverters)) {
            throw new BizException("There are no device");
        }

        siteDeviceService.updateSettingsAndNotify(inverters);
    }

    public void setSiteRoleBySerialNumber(String serialNumber){
        List<SiteCoordinatorSiteDataDO> siteDatas = siteMapper.selectSiteDataBySerialNumber(serialNumber);
        List<SiteCoordinatorSiteData> siteDataList = new ArrayList<>();
        for (SiteCoordinatorSiteDataDO siteDataDO : siteDatas) {
            LocalDateTime mDate = null;
            if (siteDataDO.getManufactureDate().length() == 8) {
                try {
                    String year = siteDataDO.getManufactureDate().substring(0, 4);
                    String month = siteDataDO.getManufactureDate().substring(4, 6);
                    String day = siteDataDO.getManufactureDate().substring(6, 8);
                    String dateStr = year + "-" + month + "-" + day;

                    mDate = LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
                } catch (DateTimeParseException e) {
                }
            }

            SiteCoordinatorSiteData siteData = new SiteCoordinatorSiteData();
            BeanUtils.copyProperties(siteDataDO, siteData);
            siteData.setRossVersion(new RossVersion(siteDataDO.getVersion()).getRossVersionNumber());
            siteData.setIsConnected(siteDataDO.getIsConnected() != null ? siteDataDO.getIsConnected() : false);
            siteData.setManufactureDate(mDate!=null?mDate:LocalDateTime.now());
            siteDataList.add(siteData);
        }

        String coordinator = SiteCoordinatorHelper.decideCoordinator(siteDataList.stream().map(e->{
            DecideCoordinatorDto coordinatorDto = new DecideCoordinatorDto();
            coordinatorDto.setRossSerialNumber(e.getSerialNumber());
            coordinatorDto.setIsConnected(e.getIsConnected());
            coordinatorDto.setRossVersion(e.getRossVersion());
            coordinatorDto.setModel(e.getModel());
            coordinatorDto.setManufactureDate(e.getManufactureDate());
            coordinatorDto.setManufactureSerialNumber(e.getManufactureSerialNumber());
            coordinatorDto.setSiteRole(e.getSiteRole());
            coordinatorDto.setIsGridTie(e.getIsGridTie());
            return coordinatorDto;
        }).collect(Collectors.toList()));

        List<SetSiteCoordinatorDataInDto> newRoles = siteDataList.stream()
                .map(p -> {
                    SetSiteCoordinatorDataInDto dto = new SetSiteCoordinatorDataInDto();
                    dto.setSerialNumber(p.getSerialNumber());

                    String role = p.getSerialNumber().equals(coordinator)
                            ? PhaseRole.Coordinator.name()
                            : SiteCoordinatorHelper.canBeCoordinatorOrWorker(
                            p.getIsConnected() != null ? p.getIsConnected() : false,
                            p.getRossVersion(),
                            p.getIsGridTie())
                            ? PhaseRole.Worker.name()
                            : p.getIsGridTie()
                            ? PhaseRole.GridTie.name()
                            : PhaseRole.Incompatible.name();

                    dto.setRole(role);
                    return dto;
                })
                .collect(Collectors.toList());

        String validationError = validateSiteCoordinator(newRoles);
        if (StringUtils.isNotBlank(validationError)) {
            throw new BizException(validationError);
        }
        
        for (SetSiteCoordinatorDataInDto newRole : newRoles) {
            productInstallationMapper.updatePhaseRole(newRole.getSerialNumber(), newRole.getRole());
        }
    }

    private String validateSiteCoordinator(List<SetSiteCoordinatorDataInDto> siteData) {
        return null;
    }

    private void setComputerName(SiteInverterInfoVO item) {
        String computerName = item.getComputerName();
        if (StringUtils.isBlank(computerName)) {
            if (item.getLatestTelemetryUtc() != null && StringUtils.isNotEmpty(item.getSerialNumber())) {
                String serialNumber = item.getSerialNumber();
                long epoch = item.getLatestTelemetryUtc().toEpochSecond();

                TelemetryDataVO telemetryData = tableStoreServiceFactory.getService(CloudPlatformName.Tuya).getTelemetryData(serialNumber, epoch);
                String telemetry = getValue(telemetryData, TelemetryDataVO::getTelemetry);

                if (StringUtils.isBlank(telemetry)) {
                    telemetryData = tableStoreServiceFactory.getService(CloudPlatformName.Azure).getTelemetryData(serialNumber, epoch);
                    telemetry = getValue(telemetryData, TelemetryDataVO::getTelemetry);
                }

                if (StringUtils.isNoneBlank(telemetry)) {
                    RossTelemetry rossTelemetry = JSONObject.parseObject(telemetry, RossTelemetry.class);
                    if (rossTelemetry != null) {
                        TelemetryExtensions.rebuildDataBackedBands(rossTelemetry.getRossDeviceTelemetry());
                        computerName = getValue(rossTelemetry, RossTelemetry::getRossDeviceTelemetry, RossDeviceTelemetry::getOuijaBoard, OuijaBoardTelemetry::getComputerName);
                    }
                }
            }
        }

        item.setComputerName(computerName);
    }

    private String decideCurrentRole(String currentRole, String selectedRole,
                                     Version version, String model) {
        if (version != null && version.getMajor() >= 2 && version.getMinor() >= 17
                && !model.toLowerCase().startsWith("si")) {
            return currentRole;
        }

        return selectedRole;
    }

    private List<SiteInverterInfoVO> sortResults(List<SiteInverterInfoVO> result) {
        return result.stream()
                .sorted(Comparator
                        .comparingInt((SiteInverterInfoVO d) -> getRoleOrder(d.getCurrentRole()))
                        .thenComparingInt((SiteInverterInfoVO d) -> getRoleOrder(d.getSelectedRole()))
                        .thenComparing((SiteInverterInfoVO d) -> d.getSerialNumber()))
                .collect(Collectors.toList());
    }

    private int getRoleOrder(String role) {
        if (role == null) {
            return 3;
        }
        if (role.equals(ROLE_MASTER)) {
            return 1;
        }
        if (role.equals(PhaseRole.Worker.name())) {
            return 2;
        }
        return 3;
    }
}
