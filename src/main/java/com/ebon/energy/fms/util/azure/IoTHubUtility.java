package com.ebon.energy.fms.util.azure;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.config.AzureIoTConfig;
import com.ebon.energy.fms.domain.entity.DeviceControl;
import com.ebon.energy.fms.util.ReflectionUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.microsoft.azure.sdk.iot.service.exceptions.IotHubException;
import com.microsoft.azure.sdk.iot.service.messaging.DeliveryAcknowledgement;
import com.microsoft.azure.sdk.iot.service.messaging.IotHubServiceClientProtocol;
import com.microsoft.azure.sdk.iot.service.messaging.Message;
import com.microsoft.azure.sdk.iot.service.messaging.MessagingClient;
import com.microsoft.azure.sdk.iot.service.methods.DirectMethodRequestOptions;
import com.microsoft.azure.sdk.iot.service.methods.DirectMethodResponse;
import com.microsoft.azure.sdk.iot.service.methods.DirectMethodsClient;
import com.microsoft.azure.sdk.iot.service.registry.RegistryClient;
import com.microsoft.azure.sdk.iot.service.twin.Twin;
import com.microsoft.azure.sdk.iot.service.twin.TwinClient;
import com.microsoft.azure.sdk.iot.service.twin.TwinCollection;
import com.microsoft.azure.sdk.iot.service.twin.TwinState;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Component
public class IoTHubUtility implements AutoCloseable {
    private final String iotHubConnectionString;
    private final String iotHubHostName;
    private RegistryClient registryClient;
    private TwinClient twinClient;
    private DirectMethodsClient directMethodsClient;
    private MessagingClient messagingClient;
    private Long responseTimeoutSeconds = 60L;
    private Long connectTimeoutSeconds = 60L;

    private ObjectMapper objectMapper;

    @SneakyThrows
    @Autowired
    public IoTHubUtility(AzureIoTConfig azureIoTConfig, ObjectMapper objectMapper) throws IOException {
        this.iotHubConnectionString = azureIoTConfig.getConnectionString();
        this.iotHubHostName = azureIoTConfig.getHostName();
        this.registryClient = new RegistryClient(iotHubConnectionString);
        this.twinClient = new TwinClient(iotHubConnectionString);
        this.directMethodsClient = new DirectMethodsClient(iotHubConnectionString);
        this.messagingClient = new MessagingClient(iotHubConnectionString, IotHubServiceClientProtocol.AMQPS);
        this.messagingClient.open();
        this.objectMapper = objectMapper;
    }

//    @PostConstruct
//    public void init(){
//        clearSchedulesAndIncrementVersion("030c2ab7-61eb-4de1-8c90-0db854871ce6",15);
//    }

    public CompletableFuture<Void> deleteWatchdogDevices(String watchdogId) {
        return CompletableFuture.runAsync(() -> {
            try {
                registryClient.removeDevice(watchdogId);
            } catch (IotHubException | IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public CompletableFuture<Void> sendPackage(String serialNumber, String packageUri) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.APPLY_SCCM_ENROLLMENT_PACKAGE, packageUri);
    }

    public CompletableFuture<Void> sendDeviceManagementSetup(String serialNumber, String packageUri) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.UPDATE_DM_SET_UP, packageUri);
    }

    public CompletableFuture<Void> executeScript(String serialNumber, String packageUri) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.RUN_SCRIPT, packageUri);
    }

    public CompletableFuture<Void> sendDeviceInverterTime(String serialNumber, String inverterSetTime) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.SET_DEVICE_INVERTER_TIME, inverterSetTime);
    }

    public CompletableFuture<Void> sendDeviceInverterTimeFromWindowsTime(String serialNumber) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.SET_INVERTER_TIME_FROM_WINDOWS_TIME, "");
    }

    public CompletableFuture<Void> sendDeviceTimeZone(String serialNumber, String timezone) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.SET_INVERTER_TIME_ZONE, timezone);
    }

    public CompletableFuture<Void> sendResetBatteryAsync(String serialNumber) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.RESET_BATTERY, "");
    }

    public CompletableFuture<String> sendResetDisabledBatteryAsync(String deviceId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DirectMethodRequestOptions options = DirectMethodRequestOptions.builder()
                        .methodResponseTimeoutSeconds(responseTimeoutSeconds.intValue())
                        .methodConnectTimeoutSeconds(connectTimeoutSeconds.intValue())
                        .build();
                DirectMethodResponse response = directMethodsClient.invoke(deviceId, DeviceCommandProperty.RESET_DISABLED_BATTERY, options);
                return response.getPayloadAsJsonString();
            } catch (IotHubException | IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public CompletableFuture<Void> sendApplyWindowsUpdateSettings(String serialNumber, String windowsOSSettingsPackageUri) {
        return sendMessageAsync(serialNumber, DeviceCommandProperty.APPLY_WINDOWS_UPDATE_PACKAGE, windowsOSSettingsPackageUri);
    }

    private CompletableFuture<Void> sendMessageAsync(String serial, String command, String value) {
        return CompletableFuture.runAsync(() -> {
            try {
                Message message = new Message(value.getBytes(StandardCharsets.UTF_8));
                message.setDeliveryAcknowledgement(DeliveryAcknowledgement.Full);
                Map<String, String> parameters = new HashMap<>();
                parameters.put("CMD", command);
                parameters.put("VAL", value);
                message.setProperties(parameters);
                messagingClient.send(serial, message);
            } catch (IotHubException e) {
                throw new RuntimeException(e);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    public CompletableFuture<String> sendRebootCommandAsync(String deviceId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DirectMethodRequestOptions options = DirectMethodRequestOptions.builder()
                        .methodResponseTimeoutSeconds(responseTimeoutSeconds.intValue())
                        .methodConnectTimeoutSeconds(connectTimeoutSeconds.intValue())
                        .build();
                DirectMethodResponse response = directMethodsClient.invoke(deviceId, "reboot", options);
                return response.getPayloadAsJsonString();
            } catch (IotHubException | IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public CompletableFuture<String> sendMeterTestCommandAsync(String deviceId, String methodBodyJson) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DirectMethodRequestOptions options = DirectMethodRequestOptions.builder()
                        .methodResponseTimeoutSeconds(responseTimeoutSeconds.intValue())
                        .methodConnectTimeoutSeconds(connectTimeoutSeconds.intValue())
                        .payload(methodBodyJson)
                        .build();
                DirectMethodResponse response = directMethodsClient.invoke(deviceId, "queuemetertest", options);
                return response.getPayloadAsJsonString();
            } catch (IotHubException | IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public CompletableFuture<String> sendDirectMethodCommandAsync(String deviceId, String directMethodCommand) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DirectMethodRequestOptions options = DirectMethodRequestOptions.builder()
                        .methodResponseTimeoutSeconds(responseTimeoutSeconds.intValue())
                        .methodConnectTimeoutSeconds(connectTimeoutSeconds.intValue())
                        .build();
                DirectMethodResponse response = directMethodsClient.invoke(deviceId, directMethodCommand, options);
                return response.getPayloadAsJsonString();
            } catch (IotHubException | IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public CompletableFuture<DirectMethodResponse> sendDirectMethodWithPayload(String deviceId, String directMethodName, String payload) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DirectMethodRequestOptions options = DirectMethodRequestOptions.builder()
                        .methodResponseTimeoutSeconds(responseTimeoutSeconds.intValue())
                        .methodConnectTimeoutSeconds(connectTimeoutSeconds.intValue())
                        .payload(payload)
                        .build();
                return directMethodsClient.invoke(deviceId, directMethodName, options);
            } catch (SocketTimeoutException e) {
                throw new RuntimeException("The operation failed because the requested device isn't online");
            } catch (IotHubException | IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public CompletableFuture<String> sendDirectMethodWithoutPayload(String deviceId, String directMethodName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DirectMethodRequestOptions options = DirectMethodRequestOptions.builder()
                        .methodResponseTimeoutSeconds(responseTimeoutSeconds.intValue())
                        .methodConnectTimeoutSeconds(connectTimeoutSeconds.intValue())
                        .build();
                DirectMethodResponse response = directMethodsClient.invoke(deviceId, directMethodName, options);
                return response.getPayloadAsJsonString();
            } catch (IotHubException | IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 清空设备孪生中的schedules并增加版本号
     * @param deviceId 设备ID
     * @param currentVersion 当前版本号
     * @return 是否成功
     */

    public Boolean updateDeviceTwin(String deviceId, String desiredConfig) {
        try {
            // Parse the input JSON
            JsonNode patchNode = objectMapper.readTree(desiredConfig);

            // Check if the input JSON has the expected structure with properties.desired
            if (patchNode.has("properties") && patchNode.get("properties").has("desired")) {
                // Convert the desired properties to a TwinCollection
                String desiredJson = objectMapper.writeValueAsString(patchNode.get("properties").get("desired").get("settings"));
                TwinCollection desiredProperties = objectMapper.readValue(desiredJson, TwinCollection.class);

                // Create a new Twin object for the patch
                Twin twinPatch =  twinClient.get(deviceId);

                TwinCollection oldDesiredProperties = twinPatch.getDesiredProperties();

                oldDesiredProperties.put("settings", desiredProperties);

//                Twin newTwin = JSON.parseObject(JSONObject.toJSONString(oldDesiredProperties), Twin.class);
//                Map<String, Object> updates = new HashMap<>();
//                updates.put("deviceId", deviceId);
//                ReflectionUtils.setFields(newTwin, updates);
                // Apply the patch directly - this handles setting fields to null/empty properly
                twinClient.patch(twinPatch);
                return true;
            } else {
                throw new IllegalArgumentException("Input JSON must contain 'properties.desired' structure");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // 递归深度合并：将 patch 合并进 target，不覆盖完整对象，仅更新字段
    public static void deepMerge(ObjectNode target, ObjectNode patch) {
        Iterator<Map.Entry<String, JsonNode>> fields = patch.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String fieldName = entry.getKey();
            JsonNode patchValue = entry.getValue();

            JsonNode targetValue = target.get(fieldName);

            if (targetValue != null && targetValue.isObject() && patchValue.isObject()) {
                // 递归合并子对象
                deepMerge((ObjectNode) targetValue, (ObjectNode) patchValue);
            } else {
                // 替换或新增字段
                target.set(fieldName, patchValue);
            }
        }
    }

    public String buildRossTwoUpdateString(String majorVersion, String minorVersion, String buildNumber,
                                           String accountName, String accountKey) {
        String header = buildUpdateHeaderPart(accountName, accountKey);
        String rosstwopart = buildRossTwoAppPart(majorVersion, minorVersion, buildNumber);
        String watchdogpart = buildWatchdogAppPart(majorVersion, minorVersion, buildNumber);
        String footerpart = buildUpdateFooterPart();
        return header + rosstwopart + watchdogpart + footerpart;
    }

    @SneakyThrows
    public void sendDeviceControl(String serialNumber, DeviceControl deviceControl) {
        var productControl = objectMapper.writeValueAsString(deviceControl);
        sendMessageAsync(serialNumber, DeviceCommandProperty.ApplyUserSetting, productControl);
    }

    private String buildUpdateHeaderPart(String accountName, String accountKey) {
        String endpoint = "BlobEndpoint=https://" + accountName + ".blob.core.windows.net/;";
        return "\"windows\": { \"externalStorage\": {  \"connectionString\": \"DefaultEndpointsProtocol=https;AccountName=" +
                accountName + ";AccountKey=" + accountKey + ";" + endpoint + "\" }, \"apps\": {";
    }

    private String buildUpdateFooterPart() {
        return " } },";
    }

    private String buildRossTwoAppPart(String majorVersion, String minorVersion, String buildNumber) {
        String baseFolder = "ross-" + buildNumber;
        String version = majorVersion + "." + minorVersion + "." + buildNumber;

        String appName = "RedbackTechnologies_ROSS_3ym0hckg60t6j";
        String familyName = "RedbackTechnologies.ROSS_3ym0hckg60t6j";
        String depsSource = netNativeDependencies(baseFolder);

        return "\"" + appName + "\": { \"pkgFamilyName\": \"" + familyName + "\", \"version\": \"" + version +
                ".0\", \"startUp\": \"foreground\", \"appxSource\": \"" + baseFolder + "/Redback.Ross_" + version +
                ".0_ARM.appx\"," + depsSource + "\"certSource\": \"" + baseFolder + "/Redback.Ross_" + version +
                ".0_ARM.cer\", \"certStore\": \"./Device/Vendor/MSFT/RootCATrustedCertificates/TrustedPeople\" }, ";
    }

    private String buildWatchdogAppPart(String majorVersion, String minorVersion, String buildNumber) {
        String baseFolder = "wd-" + buildNumber;
        String version = majorVersion + "." + minorVersion + "." + buildNumber;

        String appName = "RedbackTechnologies_Watchdog_00xfq0dct0ttr";
        String familyName = "RedbackTechnologies.Watchdog_00xfq0dct0ttr";
        String depsSource = netNativeDependencies(baseFolder);

        return "\"" + appName + "\": { \"pkgFamilyName\": \"" + familyName + "\", \"version\": \"" + version +
                ".0\", \"startUp\": \"background\", \"appxSource\": \"" + baseFolder + "/Redback.Watchdog_" + version +
                ".0_ARM.appx\", " + depsSource + " \"certSource\": \"" + baseFolder + "/Redback.Watchdog_" + version +
                ".0_ARM.cer\", \"certStore\": \"./Device/Vendor/MSFT/RootCATrustedCertificates/TrustedPeople\" }, ";
    }

    private String netNativeDependencies(String dependenciesFolder) {
        return "\"depsSources\": \"" + dependenciesFolder + "/Microsoft.NET.Native.Framework.2.1.appx;" +
                dependenciesFolder + "/Microsoft.NET.Native.Runtime.2.1.appx;" +
                dependenciesFolder + "/Microsoft.VCLibs.ARM.14.00.appx\",";
    }

    @Override
    @PreDestroy
    public void close() throws Exception {
        if (messagingClient != null) {
            messagingClient.close();
        }
    }

    // Device command properties
    private static class DeviceCommandProperty {
        static final String APPLY_SCCM_ENROLLMENT_PACKAGE = "ApplySccmEnrollmentPackage";
        static final String UPDATE_DM_SET_UP = "UpdateDMSetUp";
        static final String RUN_SCRIPT = "RunScript";
        static final String SET_DEVICE_INVERTER_TIME = "SetDeviceInverterTime";
        static final String SET_INVERTER_TIME_FROM_WINDOWS_TIME = "SetInverterTimeFromWindowsTime";
        static final String SET_INVERTER_TIME_ZONE = "SetInverterTimeZone";
        static final String RESET_BATTERY = "ResetBattery";
        static final String RESET_DISABLED_BATTERY = "ResetDisabledBattery";
        static final String APPLY_WINDOWS_UPDATE_PACKAGE = "ApplyWindowsUpdatePackage";
        static final String ApplyUserSetting = "ApplyUserSetting";

    }
}