package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.domain.po.SiteInverterAddPO;
import com.ebon.energy.fms.domain.po.SiteInverterUpdatePO;
import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.site.SiteHistoryVO;
import com.ebon.energy.fms.service.SiteDashboardHistoryService;
import com.ebon.energy.fms.service.SiteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 站点管理
 */
@Slf4j
@RestController
@RequestMapping("/api/site")
public class SiteController {

    @Resource
    private SiteService siteService;

    @Resource
    private SiteDashboardHistoryService siteDashboardHistoryService;

    /**
     * 站点列表查询
     *
     * @param po
     * @return
     */
    @GetMapping("/list")
    public JsonResult<PageResult<SiteVO>> list(SiteListPO po) {
        return JsonResult.buildSuccess(siteService.getSiteList(po));
    }

    /**
     * 站点逆变器列表
     *
     * @param siteId 站点ID
     * @return
     */
    @GetMapping("/inverter-list")
    public JsonResult<List<SiteInverterInfoVO>> siteInverterList(@RequestParam("siteId") String siteId) {
        return JsonResult.buildSuccess(siteService.getSiteInverterList(siteId));
    }

    /**
     * 站点逆变器更新
     * 
     * @param updatePO 
     * @return
     */
    @PostMapping("/inverter-update")
    public JsonResult inverterUpdate(@RequestBody SiteInverterUpdatePO updatePO) {
        siteService.updateInverterSelectedRole(updatePO);
        return JsonResult.buildSuccess();
    }

    /**
     * 站点逆变器增加
     *
     * @param addPO
     * @return
     */
    @PostMapping("/inverter-add")
    public JsonResult inverterMerge(@RequestBody SiteInverterAddPO addPO) {
        siteService.addInverter(addPO);
        return JsonResult.buildSuccess();
    }

    /**
     * 站点逆变器角色列表
     *
     * @return
     */
    @GetMapping("/phase-role/list")
    public JsonResult<List<PhaseRoleVO>> phaseRoles() {
        return JsonResult.buildSuccess(PhaseRole.getPhaseRoles());
    }

    /**
     * 站点历史电量统计
     *
     * @param siteId 站点ID
     * @return
     */
    @GetMapping("/history")
    public JsonResult<SiteHistoryVO> siteHistory(@RequestParam("siteId") String siteId) {
        return JsonResult.buildSuccess(siteDashboardHistoryService.getSiteHistory(siteId, 7));
    }

}
