package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.RoleListPO;
import com.ebon.energy.fms.domain.po.RoleModifyPO;
import com.ebon.energy.fms.domain.po.RolePO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.RoleDropDownVO;
import com.ebon.energy.fms.domain.vo.RoleVO;
import com.ebon.energy.fms.service.RoleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 角色管理
 */
@RestController
@RequestMapping("/api/role")
public class RoleController {

    @Resource
    private RoleService roleService;

    /**
     * 角色列表查询
     *
     * @return
     */
    @GetMapping("/list")
    public JsonResult<PageResult<RoleVO>> list(RoleListPO roleListPO) {
        return JsonResult.buildSuccess(roleService.getRoleList(roleListPO));
    }

    /**
     * 角色下拉框列表查询
     *
     * @return
     */
    @GetMapping("/dropdown")
    public JsonResult<List<RoleDropDownVO>> dropdown() {
        return JsonResult.buildSuccess(roleService.getRoleDropdownList());
    }

    /**
     * 新增角色
     *
     * @param rolePO
     * @return
     */
    @OperateLogAnnotation(name="Add Role")
    @PostMapping("/add")
    public JsonResult add(@RequestBody @Valid RolePO rolePO) {
        roleService.addRole(rolePO);
        return JsonResult.buildSuccess();
    }

    /**
     * 修改角色
     *
     * @param roleModifyPO
     * @return
     */
    @OperateLogAnnotation(name="Update Role")
    @PostMapping("/modify")
    public JsonResult modify(@RequestBody @Valid RoleModifyPO roleModifyPO) {
        roleService.modifyRole(roleModifyPO);
        return JsonResult.buildSuccess();
    }

}
